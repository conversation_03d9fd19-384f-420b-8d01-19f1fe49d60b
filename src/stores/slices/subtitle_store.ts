import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// ==============================
// Type Definitions
// ==============================

export type SubtitleItem = {
  id: string;
  startTime: string; // Format: "HH:MM:SS,mmm"
  endTime: string;   // Format: "HH:MM:SS,mmm"
  text: string;
  translatedText?: string;
};

export type SubtitleData = {
  prompt?: string;
  originalSubtitles: SubtitleItem[];
  translatedSubtitles: SubtitleItem[];
};

// ==============================
// Default Values
// ==============================

export const defaultSubtitleData: SubtitleData = {
  prompt: "",
  originalSubtitles: [],
  translatedSubtitles: [],
};

// ==============================
// Atoms
// ==============================

export const subtitleDataAtom = atomWithStorage<SubtitleData>(
  "subtitle-data",
  defaultSubtitleData,
  undefined,
  {
    getOnInit: true,
  }
);

// Atom to track which subtitle is currently being edited
export const editingSubtitleIdAtom = atom<string | null>(null);

// Atom to track the current editing mode (editing or preview)
export const subtitleEditingModeAtom = atom<"editing" | "preview">("editing");

// Derived atom to get original subtitles
export const originalSubtitlesAtom = atom(
  (get) => get(subtitleDataAtom).originalSubtitles
);

// Derived atom to get translated subtitles
export const translatedSubtitlesAtom = atom(
  (get) => get(subtitleDataAtom).translatedSubtitles
);

// Action atoms for managing subtitles
export const addSubtitleAtom = atom(
  null,
  (get, set, newSubtitle: Omit<SubtitleItem, "id">) => {
    const currentData = get(subtitleDataAtom);
    const id = Date.now().toString();
    const subtitle: SubtitleItem = { ...newSubtitle, id };
    
    set(subtitleDataAtom, {
      ...currentData,
      originalSubtitles: [...currentData.originalSubtitles, subtitle],
      translatedSubtitles: [...currentData.translatedSubtitles, { ...subtitle, translatedText: "" }],
    });
  }
);

export const updateSubtitleAtom = atom(
  null,
  (get, set, { id, updates, type }: {
    id: string;
    updates: Partial<SubtitleItem>;
    type: "original" | "translated"
  }) => {
    const currentData = get(subtitleDataAtom);

    if (type === "original") {
      const updatedOriginal = currentData.originalSubtitles.map(sub =>
        sub.id === id ? { ...sub, ...updates } : sub
      );
      // Also update the corresponding translated subtitle if translatedText is provided
      let updatedTranslated = currentData.translatedSubtitles;
      if (updates.translatedText !== undefined) {
        updatedTranslated = currentData.translatedSubtitles.map(sub =>
          sub.id === id ? { ...sub, translatedText: updates.translatedText } : sub
        );
      }

      set(subtitleDataAtom, {
        ...currentData,
        originalSubtitles: updatedOriginal,
        translatedSubtitles: updatedTranslated,
      });
    } else {
      const updatedTranslated = currentData.translatedSubtitles.map(sub =>
        sub.id === id ? { ...sub, ...updates } : sub
      );
      set(subtitleDataAtom, {
        ...currentData,
        translatedSubtitles: updatedTranslated,
      });
    }
  }
);

// New atom for updating both original and translated at once
export const updateBothSubtitlesAtom = atom(
  null,
  (get, set, { id, updates }: { id: string; updates: Partial<SubtitleItem> }) => {
    const currentData = get(subtitleDataAtom);

    const updatedOriginal = currentData.originalSubtitles.map(sub =>
      sub.id === id ? { ...sub, ...updates } : sub
    );

    const updatedTranslated = currentData.translatedSubtitles.map(sub =>
      sub.id === id ? { ...sub, ...updates } : sub
    );

    set(subtitleDataAtom, {
      ...currentData,
      originalSubtitles: updatedOriginal,
      translatedSubtitles: updatedTranslated,
    });
  }
);

export const deleteSubtitleAtom = atom(
  null,
  (get, set, id: string) => {
    const currentData = get(subtitleDataAtom);
    
    set(subtitleDataAtom, {
      ...currentData,
      originalSubtitles: currentData.originalSubtitles.filter(sub => sub.id !== id),
      translatedSubtitles: currentData.translatedSubtitles.filter(sub => sub.id !== id),
    });
  }
);

export const clearSubtitlesAtom = atom(
  null,
  (get, set) => {
    set(subtitleDataAtom, defaultSubtitleData);
  }
);

// Utility functions for time conversion
export const timeStringToSeconds = (timeString: string): number => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + (parseInt(milliseconds) / 1000);
};

export const secondsToTimeString = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};
