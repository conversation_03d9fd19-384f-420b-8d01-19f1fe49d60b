import { CreateTaskStackPage } from "@/components/stack/create-task-panel/stack-pages";
import { atomWithStorage } from "jotai/utils";
import { atom } from "jotai";

export const createTaskStackAtom = atomWithStorage<CreateTaskStackPage>(
  "create-task-stack",
  "upload"
);

// Atom to track if subtitle editor is open (to hide task history panel)
export const isSubtitleEditorOpenAtom = atom<boolean>(
  (get) => {
    const currentStack = get(createTaskStackAtom);
    return currentStack === "subtitle-editor-with-video" || currentStack === "subtitle-editor";
  }
);
