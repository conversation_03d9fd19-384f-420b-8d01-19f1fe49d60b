import <PERSON>ie, { Table } from "dexie";
import { ManagedTask } from "@/types/task-manager";

class VideoTranslationDB extends <PERSON><PERSON> {
  tasks!: Table<ManagedTask>;

  constructor() {
    super("VideoTranslationDB");
    this.version(3).stores({
      tasks:
        "id, name, createdAt, updatedAt, status, isActive, currentStep, settings.sourceLanguage, settings.targetLanguage",
    });
  }
}

export const db = new VideoTranslationDB();
