import { useMemo, useEffect, useRef } from "react";

export function useProxyImage() {
  // Use ref to persist cache across re-renders
  const blobUrlCacheRef = useRef(new Map<string, string>());

  const getProxyUrl = useMemo(() => {
    return (url: string) => {
      if (!url) return "";

      // Convert base64 data URLs to blob URLs to avoid header size issues
      if (url.startsWith('data:')) {
        // Check cache first
        if (blobUrlCacheRef.current.has(url)) {
          return blobUrlCacheRef.current.get(url)!;
        }

        try {
          // Extract the base64 data and mime type
          const [header, base64Data] = url.split(',');
          const mimeType = header.match(/data:([^;]+)/)?.[1] || 'image/jpeg';

          // Convert base64 to blob
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: mimeType });

          // Create blob URL
          const blobUrl = URL.createObjectURL(blob);
          console.log('[PROXY_IMAGE] Converted base64 to blob URL:', blobUrl);

          // Cache the blob URL
          blobUrlCacheRef.current.set(url, blobUrl);
          return blobUrl;
        } catch (error) {
          console.error('[PROXY_IMAGE] Failed to convert base64 to blob URL:', error);
          // Fallback to proxy URL if conversion fails
          return `/api/302/vt/image/proxy?url=${encodeURIComponent(url)}`;
        }
      }

      // For regular URLs, use proxy
      return `/api/302/vt/image/proxy?url=${encodeURIComponent(url)}`;
    };
  }, []);

  // Cleanup blob URLs on unmount
  useEffect(() => {
    return () => {
      const cache = blobUrlCacheRef.current;
      for (const [, blobUrl] of cache) {
        if (blobUrl.startsWith('blob:')) {
          console.log('[PROXY_IMAGE] Cleaning up blob URL:', blobUrl);
          URL.revokeObjectURL(blobUrl);
        }
      }
      cache.clear();
    };
  }, []);

  return {
    getProxyUrl,
  };
}
