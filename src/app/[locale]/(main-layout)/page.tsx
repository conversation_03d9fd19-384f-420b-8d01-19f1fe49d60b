"use client";

import MainLayout from "@/components/layout/main-layout";
import { CreateTaskPanel } from "@/components/panel/create-task-panel";
import { TaskHistoryPanel } from "@/components/panel/task-history-panel";
import { VideoPreviewPanel } from "@/components/panel/video-preview-panel";
import { RESIZABLE_PANELS_LAYOUT_COOKIE_NAME } from "@/constants/values";
import { useAtom, useAtomValue } from "jotai";
import { showVideoPreviewPanelAtom } from "@/stores/slices/current_task";
import { isSubtitleEditorOpenAtom } from "@/stores/slices/stack_store";
import { getCookie } from "cookies-next";
import { useEffect, useState } from "react";

export default function Home() {
  const [showVideoPreview] = useAtom(showVideoPreviewPanelAtom);
  const isSubtitleEditorOpen = useAtomValue(isSubtitleEditorOpenAtom);
  const [initialLayout, setInitialLayout] = useState([50, 50]);

  useEffect(() => {
    const cookieLayout = getCookie(RESIZABLE_PANELS_LAYOUT_COOKIE_NAME);
    if (cookieLayout) {
      try {
        setInitialLayout(JSON.parse(cookieLayout as string));
      } catch {
        setInitialLayout([50, 50]);
      }
    }
  }, []);

  return (
    <MainLayout
      initialLayout={isSubtitleEditorOpen ? [100, 0] : initialLayout}
      leftPanel={<CreateTaskPanel />}
      rightPanel={
        // Hide task history panel when subtitle editor is open
        isSubtitleEditorOpen ? null : showVideoPreview ? (
          <VideoPreviewPanel className="p-6" />
        ) : (
          <TaskHistoryPanel className="p-6" />
        )
      }
    />
  );
}
