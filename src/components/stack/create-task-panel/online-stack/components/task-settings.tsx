import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { SubtitleLayout, SubtitleStyle } from "@/stores/slices/current_task";
import { Languages, Volume2, ArrowRight, Settings2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { SubtitleSettings } from "./subtitle-settings";
import { VoiceDubbingSettings } from "./voice-dubbing-settings";
import { cn } from "@/lib/utils";

interface TaskSettingsProps {
  voiceSeparation: boolean;
  sourceLanguage: string;
  targetLanguage: string;
  subtitleLayout: SubtitleLayout;
  subtitleStyle: SubtitleStyle;
  showSubtitle: boolean;
  videoHeight?: number;
  voiceDubbingEnabled: boolean;
  voiceDubbingService: number;
  voiceDubbingVoiceName: string;
  voiceDubbingCloneVoiceFileUrl?: string;
  onVoiceSeparationChange: (enabled: boolean) => void;
  onSourceLanguageChange: (language: string) => void;
  onTargetLanguageChange: (language: string) => void;
  onSubtitleLayoutChange: (layout: SubtitleLayout) => void;
  onSubtitleStyleChange: (style: Partial<SubtitleStyle>) => void;
  onShowSubtitleChange: (show: boolean) => void;
  onVoiceDubbingEnabledChange: (enabled: boolean) => void;
  onVoiceDubbingServiceChange: (service: number) => void;
  onVoiceDubbingVoiceNameChange: (voiceName: string) => void;
  onVoiceDubbingCloneVoiceFileUrlChange: (fileUrl: string | undefined) => void;
}

// Language selection component
const LanguageSelect = ({
  value,
  onChange,
  placeholder,
  includeAuto = false,
  className,
  disabledValues = [],
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  includeAuto?: boolean;
  className?: string;
  disabledValues?: string[];
}) => {
  const t = useTranslations();

  const allLanguages = [
    { value: "en", label: t("form.fields.language.en") },
    { value: "zh", label: t("form.fields.language.zh") },
    // South East Asian Languages
    { value: "id", label: t("form.fields.language.id", { defaultMessage: "Indonesian" }) }, // Bahasa Indonesia
    { value: "ms", label: t("form.fields.language.ms", { defaultMessage: "Malay" }) },       // Bahasa Melayu
    { value: "th", label: t("form.fields.language.th", { defaultMessage: "Thai" }) },        // ภาษาไทย
    { value: "vi", label: t("form.fields.language.vi", { defaultMessage: "Vietnamese" }) },  // Tiếng Việt
    { value: "tl", label: t("form.fields.language.tl", { defaultMessage: "Tagalog" }) },     // Tagalog (Filipino)
    { value: "my", label: t("form.fields.language.my", { defaultMessage: "Burmese" }) },     // မြန်မာဘာသာ
    { value: "km", label: t("form.fields.language.km", { defaultMessage: "Khmer" }) },       // ភាសាខ្មែរ
    { value: "lo", label: t("form.fields.language.lo", { defaultMessage: "Lao" }) },        // ພາສາລາວ
    // { value: "tet", label: "Tetum" },   // Tetum (Timor-Leste) - Add if translation key exists
    // { value: "ja", label: t("form.fields.language.ja") }, // Japanese, if still needed separately
  ];

  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger
        className={cn("h-7 text-xs motion-safe:transition-colors", className)}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {includeAuto && (
          <SelectItem
            value="auto"
            className="text-xs"
            disabled={disabledValues.includes("auto")}
          >
            {t("form.fields.sourceLanguage.auto")}
          </SelectItem>
        )}
        {allLanguages.map((lang) => (
          <SelectItem
            key={lang.value}
            value={lang.value}
            className="text-xs"
            disabled={disabledValues.includes(lang.value)}
          >
            {lang.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

const SettingSection = ({
  icon: Icon,
  title,
  children,
  className,
}: {
  icon: React.ComponentType<any>;
  title: string;
  children: React.ReactNode;
  className?: string;
}) => (
  <div className={cn("space-y-2", className)}>
    <div className="flex items-center gap-1.5">
      <Icon className="size-3.5 text-muted-foreground" />
      <span className="text-xs font-medium">{title}</span>
    </div>
    {children}
  </div>
);

export const TaskSettings = ({
  voiceSeparation,
  sourceLanguage,
  targetLanguage,
  subtitleLayout,
  subtitleStyle,
  showSubtitle,
  videoHeight,
  voiceDubbingEnabled,
  voiceDubbingService,
  voiceDubbingVoiceName,
  voiceDubbingCloneVoiceFileUrl,
  onVoiceSeparationChange,
  onSourceLanguageChange,
  onTargetLanguageChange,
  onSubtitleLayoutChange,
  onSubtitleStyleChange,
  onShowSubtitleChange,
  onVoiceDubbingEnabledChange,
  onVoiceDubbingServiceChange,
  onVoiceDubbingVoiceNameChange,
  onVoiceDubbingCloneVoiceFileUrlChange,
}: TaskSettingsProps) => {
  const t = useTranslations();

  // Handle source language change
  const handleSourceLanguageChange = (newSourceLanguage: string) => {
    onSourceLanguageChange(newSourceLanguage);
    // If new source language is same as current target language, automatically adjust target language
    if (newSourceLanguage === targetLanguage) {
      // Select a different target language
      const languageOptions = [
        "en", "zh", "id", "ms", "th", "vi", "tl", "my", "km", "lo",
      ];
      const availableLanguages = languageOptions.filter(
        (lang) => lang !== newSourceLanguage
      );
      if (availableLanguages.length > 0) {
        onTargetLanguageChange(availableLanguages[0]);
      } else {
        // Fallback if somehow all languages are the same (should not happen with this list)
        onTargetLanguageChange("en"); // Default to English
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-1.5 border-b pb-2">
        <Settings2 className="size-3.5 text-primary" />
        <span className="text-xs font-medium">{t("form.fields.settings")}</span>
      </div>

      <div className="grid gap-4">
        {/* Voice separation enhancement settings */}
        <div className="flex items-center justify-between rounded-md border bg-card/50 px-2.5 py-1.5">
          <div className="flex items-center gap-1.5">
            <Volume2 className="size-3.5 text-muted-foreground" />
            <span className="text-xs">
              {t("form.fields.voiceSeparation.label")}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Label className="text-[0.7rem] text-muted-foreground">
              {voiceSeparation ? t("common.enabled") : t("common.disabled")}
            </Label>
            <Switch
              checked={voiceSeparation}
              onCheckedChange={onVoiceSeparationChange}
            />
          </div>
        </div>

        {/* Translation settings */}
        <SettingSection
          icon={Languages}
          title={t("form.fields.translation.label")}
        >
          <div className="grid grid-cols-[1fr,auto,1fr] items-center gap-1.5">
            <LanguageSelect
              value={sourceLanguage}
              onChange={handleSourceLanguageChange}
              placeholder={t("form.fields.sourceLanguage.placeholder")}
              // includeAuto
            />
            <ArrowRight className="size-3 text-muted-foreground" />
            <LanguageSelect
              value={targetLanguage}
              onChange={onTargetLanguageChange}
              placeholder={t("form.fields.targetLanguage.placeholder")}
              disabledValues={[sourceLanguage]}
            />
          </div>
        </SettingSection>

        {/* Voice Dubbing settings */}
        <VoiceDubbingSettings
          enabled={voiceDubbingEnabled}
          service={voiceDubbingService}
          voiceName={voiceDubbingVoiceName}
          cloneVoiceFileUrl={voiceDubbingCloneVoiceFileUrl}
          onEnabledChange={onVoiceDubbingEnabledChange}
          onServiceChange={onVoiceDubbingServiceChange}
          onVoiceNameChange={onVoiceDubbingVoiceNameChange}
          onCloneVoiceFileUrlChange={onVoiceDubbingCloneVoiceFileUrlChange}
        />

        {/* Subtitle settings */}
        <SubtitleSettings
          subtitleLayout={subtitleLayout}
          subtitleStyle={subtitleStyle}
          showSubtitle={showSubtitle}
          videoHeight={videoHeight}
          onLayoutChange={onSubtitleLayoutChange}
          onStyleChange={onSubtitleStyleChange}
          onShowSubtitleChange={onShowSubtitleChange}
        />
      </div>
    </div>
  );
};
