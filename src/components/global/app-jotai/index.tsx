"use client";

import { language<PERSON><PERSON>, store } from "@/stores";
import { Provider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "jotai";
import { useLocale } from "next-intl";

export default function AppJotai({ children }: { children: React.ReactNode }) {
  // Set the initial locale to the pathname locale
  const locale = useLocale();
  store.set(language<PERSON>tom, locale);
  return <JotaiProvider store={store}>{children}</JotaiProvider>;
}
