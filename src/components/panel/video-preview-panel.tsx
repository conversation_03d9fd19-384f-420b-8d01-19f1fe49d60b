"use client";

import { useAtom } from "jotai";
import {
  currentTaskAtom,
  showPreviewSubtitleAtom,
} from "@/stores/slices/current_task";
import { VideoPreview } from "@/components/common/video/video-preview";
import { useVideoInfo } from "@/hooks/swr/use-video-info";
import { isYoutubeUrl } from "@/utils/video-format";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { VideoIcon } from "lucide-react";

interface VideoPreviewPanelProps {
  className?: string;
}

export const VideoPreviewPanel = ({ className }: VideoPreviewPanelProps) => {
  const t = useTranslations();
  const [currentTask] = useAtom(currentTaskAtom);
  const [showPreviewSubtitle] = useAtom(showPreviewSubtitleAtom);

  // Get video info for online videos
  const { data: videoInfo } = useVideoInfo(
    currentTask.videoUrl && !currentTask.thumbnail ? currentTask.videoUrl : null
  );

  const isUploadedVideo = !!(
    currentTask.videoUrl &&
    currentTask.thumbnail &&
    currentTask.name &&
    currentTask.duration
  );

  const hasVideoData = videoInfo?.info || isUploadedVideo;

  if (!hasVideoData) {
    return (
      <div className={cn("flex size-full flex-col", className)}>
        <div className="border-b p-4">
          <h2 className="text-lg font-semibold">{t("video.preview")}</h2>
        </div>
        <div className="flex flex-1 items-center justify-center p-6">
          <div className="text-center text-muted-foreground">
            <VideoIcon className="mx-auto mb-2 h-12 w-12" />
            <p className="text-sm">{t("video.no_video_selected")}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex size-full flex-col", className)}>
      <div className="border-b p-4">
        <h2 className="text-lg font-semibold">{t("video.preview")}</h2>
      </div>

      {/* Sticky Video Preview */}
      <div className="flex-1 p-4">
        <div className="sticky top-4">
          <div className="overflow-hidden rounded-lg border bg-card">
            {isUploadedVideo ? (
              <VideoPreview
                thumbnail={currentTask.thumbnail!}
                title={currentTask.name!}
                url={currentTask.videoUrl!}
                duration={currentTask.duration!}
                subtitleStyle={currentTask.settings.subtitleStyle}
                subtitleLayout={currentTask.settings.subtitleLayout}
                showSubtitle={showPreviewSubtitle}
                sourceLanguage={currentTask.settings.sourceLanguage}
                targetLanguage={currentTask.settings.targetLanguage}
                width={currentTask.settings.selectedFormat?.width}
                height={currentTask.settings.selectedFormat?.height}
              />
            ) : videoInfo?.info ? (
              <VideoPreview
                thumbnail={videoInfo.info.thumbnail}
                title={videoInfo.info.title}
                url={
                  currentTask.videoUrl && isYoutubeUrl(currentTask.videoUrl)
                    ? currentTask.videoUrl
                    : currentTask.settings.selectedFormat?.url || ""
                }
                duration={videoInfo.info.duration}
                subtitleStyle={currentTask.settings.subtitleStyle}
                subtitleLayout={currentTask.settings.subtitleLayout}
                showSubtitle={showPreviewSubtitle}
                sourceLanguage={currentTask.settings.sourceLanguage}
                targetLanguage={currentTask.settings.targetLanguage}
                width={currentTask.settings.selectedFormat?.width}
                height={currentTask.settings.selectedFormat?.height}
              />
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};
